#!/usr/bin/env python3
import os
import re

def remove_swagger_imports_and_annotations(file_path):
    """移除文件中的Swagger导入和注解"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除Swagger导入
    content = re.sub(r'import io\.swagger\.annotations\.[^;]+;\n', '', content)
    content = re.sub(r'import com\.github\.xiaoymin\.knife4j\.[^;]+;\n', '', content)
    content = re.sub(r'import springfox\.[^;]+;\n', '', content)
    
    # 移除@Api注解
    content = re.sub(r'@Api\([^)]*\)\n', '', content)
    
    # 移除@ApiModel注解
    content = re.sub(r'@ApiModel\([^)]*\)\n', '', content)
    
    # 移除@ApiModelProperty注解
    content = re.sub(r'    @ApiModelProperty\([^)]*\)\n', '', content)
    
    # 移除@ApiOperation注解
    content = re.sub(r'    @ApiOperation\([^)]*\)\n', '', content)
    
    # 移除@ApiParam注解（保留参数）
    content = re.sub(r'@ApiParam\([^)]*\)\s+', '', content)
    
    # 移除@EnableSwagger2WebMvc和@EnableKnife4j注解
    content = re.sub(r'@EnableSwagger2WebMvc\n', '', content)
    content = re.sub(r'@EnableKnife4j\n', '', content)
    
    # 清理多余的空行
    content = re.sub(r'\n\n\n+', '\n\n', content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def process_java_files(directory):
    """处理目录中的所有Java文件"""
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.java'):
                file_path = os.path.join(root, file)
                print(f"Processing: {file_path}")
                remove_swagger_imports_and_annotations(file_path)

if __name__ == "__main__":
    # 处理src目录下的所有Java文件
    process_java_files("src")
    print("Swagger annotations removal completed!")
