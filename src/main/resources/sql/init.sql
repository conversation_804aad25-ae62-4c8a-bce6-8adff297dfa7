-- 创建数据库
CREATE DATABASE IF NOT EXISTS deloitte_score_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE deloitte_score_system;


-- 创建项目提交人信息表
CREATE TABLE IF NOT EXISTS project_submission (
    id INT NOT NULL AUTO_INCREMENT COMMENT '唯一主键',
    project_name VARCHAR(200) DEFAULT NULL COMMENT '项目名称',
    submitter_name VARCHAR(50) DEFAULT NULL COMMENT '提交人姓名',
    submitter_email VARCHAR(100) DEFAULT NULL COMMENT '提交人邮箱',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目提交人信息表';

-- 创建项目质量评分表
CREATE TABLE IF NOT EXISTS project_quality_review (
    id INT NOT NULL AUTO_INCREMENT COMMENT '唯一主键',
    project_name VARCHAR(200) DEFAULT NULL COMMENT '项目名称',
    submitter_name VARCHAR(50) DEFAULT NULL COMMENT '提交人姓名',
    submitter_email VARCHAR(100) DEFAULT NULL COMMENT '提交人邮箱',
    security_score DECIMAL(5,2) DEFAULT NULL COMMENT '代码安全性得分(0-100)',
    quality_score DECIMAL(5,2) DEFAULT NULL COMMENT '代码质量得分(0-100)',
    standard_score DECIMAL(5,2) DEFAULT NULL COMMENT '提交规范得分(0-100)',
    final_score DECIMAL(5,2) DEFAULT NULL COMMENT '最终得分(0-100)',
    main_issues TEXT COMMENT '主要问题',
    optimization_suggestions TEXT COMMENT '优化建议',
    review_date DATE DEFAULT NULL COMMENT '评审日期',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目质量评分表';

-- 插入测试数据
INSERT INTO user (name, age, email) VALUES
('张三', 25, '<EMAIL>'),
('李四', 30, '<EMAIL>'),
('王五', 28, '<EMAIL>');

-- 插入项目提交测试数据
INSERT INTO project_submission (project_name, submitter_name, submitter_email) VALUES
('电商平台后端系统', '张三', '<EMAIL>'),
('用户管理系统', '李四', '<EMAIL>'),
('数据分析平台', '王五', '<EMAIL>'),
('移动端API服务', '赵六', '<EMAIL>');

-- 插入项目质量评审测试数据
INSERT INTO project_quality_review (project_name, submitter_name, submitter_email, security_score, quality_score, standard_score, final_score, main_issues, optimization_suggestions, review_date) VALUES
('电商平台后端系统', '张三', '<EMAIL>', 85.50, 88.00, 82.00, 85.40, 'SQL注入风险，部分接口缺少参数验证', '建议加强输入验证，使用参数化查询', '2025-05-30'),
('用户管理系统', '李四', '<EMAIL>', 92.00, 89.50, 91.00, 90.60, '代码规范良好，测试覆盖率较高', '建议增加集成测试，优化数据库查询性能', '2025-05-29'),
('数据分析平台', '王五', '<EMAIL>', 78.00, 82.50, 85.00, 81.70, '数据处理逻辑复杂，缺少异常处理', '建议重构数据处理模块，增加错误处理机制', '2025-05-28'),
('移动端API服务', '赵六', '<EMAIL>', 88.00, 85.00, 87.50, 86.50, '接口设计合理，文档完善', '建议优化响应时间，增加缓存机制', '2025-05-27');
