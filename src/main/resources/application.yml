spring:
  datasource:
    url: ********************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
      filter:
        stat:
          enabled: true
        wall:
          enabled: true

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

server:
  port: 8081

logging:
  level:
    deloitte.score.system: info
    org.springframework: INFO

# SpringDoc OpenAPI 配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: deloitte.score.system.controller

# Knife4j 配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-version: true
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表
  production: false
