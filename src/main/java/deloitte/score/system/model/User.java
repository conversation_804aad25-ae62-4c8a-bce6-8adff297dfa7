package deloitte.score.system.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "系统用户实体")
public class User {

    @Schema(description = "用户ID", example = "1")
    private Long id;

    @Schema(description = "用户名称", example = "张三", required = true)
    private String name;

    @Schema(description = "用户年龄", example = "25")
    private Integer age;

    @Schema(description = "用户邮箱", example = "<EMAIL>")
    private String email;
}