package deloitte.score.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 项目提交人信息表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("project_submission")
@Schema(description = "项目提交人信息实体")
public class ProjectSubmission {

    /**
     * 唯一主键
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "项目提交ID", example = "1")
    private Integer id;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称", example = "电商平台后端系统", required = true)
    private String projectName;

    /**
     * 提交人姓名
     */
    @Schema(description = "提交人姓名", example = "张三", required = true)
    private String submitterName;

    /**
     * 提交人邮箱
     */
    @Schema(description = "提交人邮箱", example = "<EMAIL>", required = true)
    private String submitterEmail;
    /**
     * 0-评分未完成,1-评分已完成
     */
    @Schema(description = "状态：评分未完成,1-评分已完成")
    private String status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-05-30T17:30:00")
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    @Schema(description = "更新时间", example = "2025-05-30T17:30:00")
    private LocalDateTime updateTime;
}
