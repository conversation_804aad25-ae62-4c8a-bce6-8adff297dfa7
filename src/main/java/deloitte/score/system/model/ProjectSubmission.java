package deloitte.score.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 项目提交人信息表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("project_submission")
@ApiModel(value = "项目提交信息", description = "项目提交人信息实体")
public class ProjectSubmission {

    /**
     * 唯一主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "项目提交ID", example = "1")
    private Integer id;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称", required = true, example = "电商平台后端系统")
    private String projectName;

    /**
     * 提交人姓名
     */
    @ApiModelProperty(value = "提交人姓名", required = true, example = "张三")
    private String submitterName;

    /**
     * 提交人邮箱
     */
    @ApiModelProperty(value = "提交人邮箱", required = true, example = "<EMAIL>")
    private String submitterEmail;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2025-05-30T17:30:00")
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "更新时间", example = "2025-05-30T17:30:00")
    private LocalDateTime updateTime;
}
