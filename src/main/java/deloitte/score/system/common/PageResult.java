package deloitte.score.system.common;



import lombok.Data;

import java.util.List;

/**
 * 分页响应结果�? */
@Data

public class PageResult<T> {
    

    private List<T> records;
    

    private Long total;
    

    private Long current;
    

    private Long size;
    

    private Long pages;
    
    public PageResult() {}
    
    public PageResult(List<T> records, Long total, Long current, Long size, Long pages) {
        this.records = records;
        this.total = total;
        this.current = current;
        this.size = size;
        this.pages = pages;
    }
    
    /**
     * 构建分页结果
     */
    public static <T> PageResult<T> build(List<T> records, Long total, Long current, Long size) {
        Long pages = (total + size - 1) / size;
        return new PageResult<>(records, total, current, size, pages);
    }
}
