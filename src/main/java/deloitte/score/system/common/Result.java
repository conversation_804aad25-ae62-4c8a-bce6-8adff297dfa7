package deloitte.score.system.common;



import lombok.Data;

/**
 * 统一响应结果�? */
@Data

public class Result<T> {
    

    private Integer code;
    

    private String message;
    

    private T data;
    

    private Boolean success;
    

    private Long timestamp;
    
    public Result() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public Result(Integer code, String message, T data, Boolean success) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.success = success;
        this.timestamp = System.currentTimeMillis();
    }
    
    /**
     * 成功响应
     */
    public static <T> Result<T> success() {
        return new Result<>(200, "操作成功", null, true);
    }
    
    public static <T> Result<T> success(T data) {
        return new Result<>(200, "操作成功", data, true);
    }
    
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(200, message, data, true);
    }
    
    /**
     * 失败响应
     */
    public static <T> Result<T> error() {
        return new Result<>(500, "操作失败", null, false);
    }
    
    public static <T> Result<T> error(String message) {
        return new Result<>(500, message, null, false);
    }
    
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(code, message, null, false);
    }
    
    /**
     * 自定义响�?     */
    public static <T> Result<T> build(Integer code, String message, T data, Boolean success) {
        return new Result<>(code, message, data, success);
    }
}
