package deloitte.score.system.enums;

/**
 * WebSocket消息类型枚举
 */
public enum MessageType {
    /**
     * 连接建立
     */
    CONNECT,
    
    /**
     * 断开连接
     */
    DISCONNECT,
    
    /**
     * 聊天消息
     */
    CHAT,
    
    /**
     * 系统通知
     */
    NOTIFICATION,
    
    /**
     * 心跳检�?     */
    HEARTBEAT,
    
    /**
     * 用户状态更�?     */
    USER_STATUS,
    
    /**
     * 错误消息
     */
    ERROR
}
