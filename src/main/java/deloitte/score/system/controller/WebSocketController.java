package deloitte.score.system.controller;

import deloitte.score.system.enums.MessageType;
import deloitte.score.system.model.WebSocketMessage;



import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.socket.WebSocketSession;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket相关的REST API控制�? */

@RestController
@RequestMapping("/api/websocket")
@CrossOrigin(origins = "*")
public class WebSocketController {

    /**
     * 获取WebSocket连接信息
     */
    @GetMapping("/info")
    public Map<String, Object> getWebSocketInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("websocketUrl", "ws://localhost:8080/websocket");
        info.put("status", "active");
        info.put("supportedMessageTypes", MessageType.values());
        info.put("timestamp", LocalDateTime.now());
        return info;
    }

    /**
     * 创建测试消息
     */
    @PostMapping("/test-message")
    public WebSocketMessage createTestMessage(@RequestParam String content) {
        return WebSocketMessage.createChatMessage("test-user", "测试用户", content);
    }

    /**
     * 获取消息格式示例
     */
    @GetMapping("/message-examples")
    public Map<String, WebSocketMessage> getMessageExamples() {
        Map<String, WebSocketMessage> examples = new HashMap<>();

        // 连接消息示例
        examples.put("connect", WebSocketMessage.builder()
                .messageId("uuid-1234")
                .type(MessageType.CONNECT)
                .senderId("user123")
                .senderName("张三")
                .content("用户连接")
                .timestamp(LocalDateTime.now())
                .build());

        // 聊天消息示例
        examples.put("chat", WebSocketMessage.builder()
                .messageId("uuid-5678")
                .type(MessageType.CHAT)
                .senderId("user123")
                .senderName("张三")
                .content("大家好！")
                .timestamp(LocalDateTime.now())
                .build());

        // 私聊消息示例
        examples.put("private", WebSocketMessage.builder()
                .messageId("uuid-9012")
                .type(MessageType.CHAT)
                .senderId("user123")
                .senderName("张三")
                .receiverId("user456")
                .content("这是一条私聊消�?)
                .timestamp(LocalDateTime.now())
                .build());

        // 心跳消息示例
        examples.put("heartbeat", WebSocketMessage.builder()
                .messageId("uuid-3456")
                .type(MessageType.HEARTBEAT)
                .senderId("user123")
                .content("ping")
                .timestamp(LocalDateTime.now())
                .build());

        return examples;
    }
}
