package deloitte.score.system.controller;

import deloitte.score.system.model.ProjectQualityReview;
import deloitte.score.system.service.ProjectQualityReviewService;
import deloitte.score.system.dto.ProjectQualityReviewDTO;
import deloitte.score.system.vo.ProjectQualityReviewVO;
import deloitte.score.system.vo.PageVO;
import deloitte.score.system.vo.StatisticsVO;
import deloitte.score.system.vo.GradeVO;
import deloitte.score.system.common.Result;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目质量评分控制器
 */
@Tag(name = "项目质量评审管理", description = "项目质量评审相关接口")
@RestController
@RequestMapping("/api/project-quality-reviews")
@CrossOrigin(origins = "*")
public class ProjectQualityReviewController {

    @Autowired
    private ProjectQualityReviewService projectQualityReviewService;

    /**
     * 创建项目质量评审记录
     */
    @Operation(summary = "创建项目质量评审记录", description = "创建新的项目质量评审记录")
    @PostMapping
    public Result<ProjectQualityReviewVO> createReview(
            @Parameter(description = "项目质量评审信息", required = true) @RequestBody ProjectQualityReviewDTO reviewDTO) {
        try {
            ProjectQualityReviewVO reviewVO = projectQualityReviewService.createReview(reviewDTO);
            return Result.success("项目质量评审记录创建成功", reviewVO);
        } catch (Exception e) {
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取项目质量评审记录
     */
    @Operation(summary = "根据ID获取项目质量评审记录", description = "根据ID查询项目质量评审记录详情")
    @GetMapping("/{id}")
    public Result<ProjectQualityReviewVO> getReviewById(
            @Parameter(description = "评审记录ID", required = true) @PathVariable Integer id) {
        try {
            ProjectQualityReviewVO reviewVO = projectQualityReviewService.getReviewById(id);
            if (reviewVO != null) {
                return Result.success(reviewVO);
            } else {
                return Result.error("未找到指定的评审记录");
            }
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询项目质量评审记录
     */
    @Operation(summary = "分页查询项目质量评审记录", description = "分页查询项目质量评审记录列表")
    @GetMapping
    public Result<PageVO<ProjectQualityReviewVO>> getReviewsPage(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "项目名称") @RequestParam(required = false) String projectName,
            @Parameter(description = "提交人姓名") @RequestParam(required = false) String submitterName,
            @Parameter(description = "开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            PageVO<ProjectQualityReviewVO> pageVO = projectQualityReviewService.getReviewsPage(
                    page, size, projectName, submitterName, startDate, endDate);
            return Result.success(pageVO);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目质量评审记录
     */
    @Operation(summary = "更新项目质量评审记录", description = "更新指定的项目质量评审记录")
    @PutMapping("/{id}")
    public Result<ProjectQualityReviewVO> updateReview(
            @Parameter(description = "评审记录ID", required = true) @PathVariable Integer id,
            @Parameter(description = "项目质量评审信息", required = true) @RequestBody ProjectQualityReviewDTO reviewDTO) {
        try {
            ProjectQualityReviewVO reviewVO = projectQualityReviewService.updateReview(id, reviewDTO);
            return Result.success("项目质量评审记录更新成功", reviewVO);
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除项目质量评审记录
     */
    @Operation(summary = "删除项目质量评审记录", description = "根据ID删除项目质量评审记录")
    @DeleteMapping("/{id}")
    public Result<String> deleteReview(
            @Parameter(description = "评审记录ID", required = true) @PathVariable Integer id) {
        try {
            boolean deleted = projectQualityReviewService.deleteReview(id);
            if (deleted) {
                return Result.success("项目质量评审记录删除成功");
            } else {
                return Result.error("未找到指定的评审记录");
            }
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取评分统计信息
     */
    @Operation(summary = "获取评分统计信息", description = "获取项目质量评分的统计信息")
    @GetMapping("/statistics")
    public Result<StatisticsVO> getStatistics() {
        try {
            StatisticsVO statisticsVO = projectQualityReviewService.getStatistics();
            return Result.success(statisticsVO);
        } catch (Exception e) {
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据分数获取等级
     */
    @Operation(summary = "根据分数获取等级", description = "根据最终分数获取对应的等级")
    @GetMapping("/grade/{score}")
    public Result<GradeVO> getGradeByScore(
            @Parameter(description = "分数", required = true) @PathVariable BigDecimal score) {
        try {
            String grade = projectQualityReviewService.getScoreGrade(score);
            GradeVO gradeVO = new GradeVO(score, grade);
            return Result.success(gradeVO);
        } catch (Exception e) {
            return Result.error("获取等级失败: " + e.getMessage());
        }
    }
}
