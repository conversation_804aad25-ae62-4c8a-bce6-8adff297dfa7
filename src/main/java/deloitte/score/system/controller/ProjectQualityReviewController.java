package deloitte.score.system.controller;

import deloitte.score.system.model.ProjectQualityReview;
import deloitte.score.system.service.ProjectQualityReviewService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;



import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目质量评分控制�? */

@RestController
@RequestMapping("/api/project-quality-reviews")
@CrossOrigin(origins = "*")
public class ProjectQualityReviewController {

    @Autowired
    private ProjectQualityReviewService projectQualityReviewService;

    /**
     * 创建项目质量评审记录
     */

    @PostMapping
    public ResponseEntity<Map<String, Object>> createReview(
            @RequestBody ProjectQualityReview review) {
        Map<String, Object> response = new HashMap<>();
        try {
            ProjectQualityReview created = projectQualityReviewService.createReview(review);
            response.put("success", true);
            response.put("message", "项目质量评审记录创建成功");
            response.put("data", created);
            response.put("grade", projectQualityReviewService.getScoreGrade(created.getFinalScore()));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "创建失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 更新项目质量评审记录
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateReview(@PathVariable Integer id, @RequestBody ProjectQualityReview review) {
        Map<String, Object> response = new HashMap<>();
        try {
            review.setId(id);
            ProjectQualityReview updated = projectQualityReviewService.updateReview(review);
            response.put("success", true);
            response.put("message", "项目质量评审记录更新成功");
            response.put("data", updated);
            response.put("grade", projectQualityReviewService.getScoreGrade(updated.getFinalScore()));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据ID查询项目质量评审记录
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getReviewById(@PathVariable Integer id) {
        Map<String, Object> response = new HashMap<>();
        try {
            ProjectQualityReview review = projectQualityReviewService.getReviewById(id);
            if (review != null) {
                response.put("success", true);
                response.put("data", review);
                response.put("grade", projectQualityReviewService.getScoreGrade(review.getFinalScore()));
            } else {
                response.put("success", false);
                response.put("message", "未找到指定的项目质量评审记录");
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 删除项目质量评审记录
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteReview(@PathVariable Integer id) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean deleted = projectQualityReviewService.deleteReview(id);
            if (deleted) {
                response.put("success", true);
                response.put("message", "项目质量评审记录删除成功");
            } else {
                response.put("success", false);
                response.put("message", "删除失败，记录不存在");
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 分页查询项目质量评审记录
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getReviews(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        Map<String, Object> response = new HashMap<>();
        try {
            Page<ProjectQualityReview> page = projectQualityReviewService.getReviewsByPage(pageNum, pageSize);

            // 为每个记录添加等级信�?            page.getRecords().forEach(review -> {
                review.setMainIssues(review.getMainIssues() + " [等级: " +
                    projectQualityReviewService.getScoreGrade(review.getFinalScore()) + "]");
            });

            response.put("success", true);
            response.put("data", page.getRecords());
            response.put("total", page.getTotal());
            response.put("pageNum", page.getCurrent());
            response.put("pageSize", page.getSize());
            response.put("pages", page.getPages());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据项目名称查询评审记录
     */
    @GetMapping("/project/{projectName}")
    public ResponseEntity<Map<String, Object>> getReviewsByProjectName(@PathVariable String projectName) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ProjectQualityReview> reviews = projectQualityReviewService.getReviewsByProjectName(projectName);
            response.put("success", true);
            response.put("data", reviews);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据得分范围查询评审记录
     */
    @GetMapping("/score-range")
    public ResponseEntity<Map<String, Object>> getReviewsByScoreRange(
            @RequestParam BigDecimal minScore,
            @RequestParam BigDecimal maxScore) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ProjectQualityReview> reviews = projectQualityReviewService.getReviewsByScoreRange(minScore, maxScore);
            response.put("success", true);
            response.put("data", reviews);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据评审日期范围查询评审记录
     */
    @GetMapping("/date-range")
    public ResponseEntity<Map<String, Object>> getReviewsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ProjectQualityReview> reviews = projectQualityReviewService.getReviewsByDateRange(startDate, endDate);
            response.put("success", true);
            response.put("data", reviews);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 搜索项目质量评审记录
     */
    @GetMapping("/search")
    public ResponseEntity<Map<String, Object>> searchReviews(@RequestParam String keyword) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ProjectQualityReview> reviews = projectQualityReviewService.searchReviews(keyword);
            response.put("success", true);
            response.put("data", reviews);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "搜索失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 查询最高得分的项目
     */
    @GetMapping("/top-scores")
    public ResponseEntity<Map<String, Object>> getTopScoreProjects(@RequestParam(defaultValue = "10") int limit) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ProjectQualityReview> reviews = projectQualityReviewService.getTopScoreProjects(limit);
            response.put("success", true);
            response.put("data", reviews);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 查询最近的评审记录
     */
    @GetMapping("/recent")
    public ResponseEntity<Map<String, Object>> getRecentReviews(@RequestParam(defaultValue = "10") int limit) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ProjectQualityReview> reviews = projectQualityReviewService.getRecentReviews(limit);
            response.put("success", true);
            response.put("data", reviews);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        Map<String, Object> response = new HashMap<>();
        try {
            long totalCount = projectQualityReviewService.getReviewCount();
            ProjectQualityReview averageScores = projectQualityReviewService.getAverageScores();
            List<ProjectQualityReview> topProjects = projectQualityReviewService.getTopScoreProjects(5);
            List<ProjectQualityReview> recentReviews = projectQualityReviewService.getRecentReviews(5);

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalReviews", totalCount);
            statistics.put("averageScores", averageScores);
            statistics.put("topProjects", topProjects);
            statistics.put("recentReviews", recentReviews);

            response.put("success", true);
            response.put("data", statistics);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 批量评分
     */
    @PostMapping("/batch-score")
    public ResponseEntity<Map<String, Object>> batchScore(@RequestBody List<ProjectQualityReview> reviews) {
        Map<String, Object> response = new HashMap<>();
        try {
            int successCount = 0;
            int failCount = 0;

            for (ProjectQualityReview review : reviews) {
                try {
                    projectQualityReviewService.createReview(review);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                }
            }

            response.put("success", true);
            response.put("message", String.format("批量评分完成，成�? %d, 失败: %d", successCount, failCount));
            response.put("successCount", successCount);
            response.put("failCount", failCount);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "批量评分失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
