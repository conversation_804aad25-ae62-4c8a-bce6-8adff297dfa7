package deloitte.score.system.controller;

import deloitte.score.system.model.ProjectQualityReview;
import deloitte.score.system.service.ProjectQualityReviewService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目质量评分控制器
 */
@Tag(name = "项目质量评审管理", description = "项目质量评审相关接口")
@RestController
@RequestMapping("/api/project-quality-reviews")
@CrossOrigin(origins = "*")
public class ProjectQualityReviewController {

    @Autowired
    private ProjectQualityReviewService projectQualityReviewService;

    /**
     * 创建项目质量评审记录
     */
    @Operation(summary = "创建项目质量评审记录", description = "创建新的项目质量评审记录")
    @PostMapping
    public ResponseEntity<Map<String, Object>> createReview(
            @Parameter(description = "项目质量评审信息", required = true) @RequestBody ProjectQualityReview review) {
        Map<String, Object> response = new HashMap<>();
        try {
            ProjectQualityReview created = projectQualityReviewService.createReview(review);
            response.put("success", true);
            response.put("message", "项目质量评审记录创建成功");
            response.put("data", created);
            response.put("grade", projectQualityReviewService.getScoreGrade(created.getFinalScore()));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "创建失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据ID获取项目质量评审记录
     */
    @ApiOperation(value = "根据ID获取项目质量评审记录", notes = "根据ID查询项目质量评审记录详情")
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getReviewById(
            @ApiParam(value = "评审记录ID", required = true) @PathVariable Integer id) {
        Map<String, Object> response = new HashMap<>();
        try {
            ProjectQualityReview review = projectQualityReviewService.getReviewById(id);
            if (review != null) {
                response.put("success", true);
                response.put("data", review);
                response.put("grade", projectQualityReviewService.getScoreGrade(review.getFinalScore()));
            } else {
                response.put("success", false);
                response.put("message", "未找到指定的评审记录");
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 分页查询项目质量评审记录
     */
    @ApiOperation(value = "分页查询项目质量评审记录", notes = "分页查询项目质量评审记录列表")
    @GetMapping
    public ResponseEntity<Map<String, Object>> getReviewsPage(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") int page,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") int size,
            @ApiParam(value = "项目名称") @RequestParam(required = false) String projectName,
            @ApiParam(value = "提交人姓名") @RequestParam(required = false) String submitterName,
            @ApiParam(value = "开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @ApiParam(value = "结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        Map<String, Object> response = new HashMap<>();
        try {
            Page<ProjectQualityReview> reviewPage = projectQualityReviewService.getReviewsPage(
                    page, size, projectName, submitterName, startDate, endDate);

            response.put("success", true);
            response.put("data", reviewPage.getRecords());
            response.put("total", reviewPage.getTotal());
            response.put("pages", reviewPage.getPages());
            response.put("current", reviewPage.getCurrent());
            response.put("size", reviewPage.getSize());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 更新项目质量评审记录
     */
    @ApiOperation(value = "更新项目质量评审记录", notes = "更新指定的项目质量评审记录")
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateReview(
            @ApiParam(value = "评审记录ID", required = true) @PathVariable Integer id,
            @ApiParam(value = "项目质量评审信息", required = true) @RequestBody ProjectQualityReview review) {
        Map<String, Object> response = new HashMap<>();
        try {
            review.setId(id);
            ProjectQualityReview updated = projectQualityReviewService.updateReview(review);
            response.put("success", true);
            response.put("message", "项目质量评审记录更新成功");
            response.put("data", updated);
            response.put("grade", projectQualityReviewService.getScoreGrade(updated.getFinalScore()));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 删除项目质量评审记录
     */
    @ApiOperation(value = "删除项目质量评审记录", notes = "根据ID删除项目质量评审记录")
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteReview(
            @ApiParam(value = "评审记录ID", required = true) @PathVariable Integer id) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean deleted = projectQualityReviewService.deleteReview(id);
            if (deleted) {
                response.put("success", true);
                response.put("message", "项目质量评审记录删除成功");
            } else {
                response.put("success", false);
                response.put("message", "未找到指定的评审记录");
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取评分统计信息
     */
    @ApiOperation(value = "获取评分统计信息", notes = "获取项目质量评分的统计信息")
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> statistics = projectQualityReviewService.getStatistics();
            response.put("success", true);
            response.put("data", statistics);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据分数获取等级
     */
    @ApiOperation(value = "根据分数获取等级", notes = "根据最终分数获取对应的等级")
    @GetMapping("/grade/{score}")
    public ResponseEntity<Map<String, Object>> getGradeByScore(
            @ApiParam(value = "分数", required = true) @PathVariable BigDecimal score) {
        Map<String, Object> response = new HashMap<>();
        try {
            String grade = projectQualityReviewService.getScoreGrade(score);
            response.put("success", true);
            response.put("score", score);
            response.put("grade", grade);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取等级失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
