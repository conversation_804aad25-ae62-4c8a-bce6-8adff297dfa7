package deloitte.score.system.controller;

import deloitte.score.system.model.ProjectSubmission;
import deloitte.score.system.service.ProjectSubmissionService;
import deloitte.score.system.dto.ProjectSubmissionDTO;
import deloitte.score.system.dto.BatchProjectSubmissionDTO;
import deloitte.score.system.dto.ProjectSubmissionPageDTO;
import deloitte.score.system.vo.ProjectSubmissionVO;
import deloitte.score.system.vo.BatchProjectSubmissionVO;
import deloitte.score.system.vo.PageVO;
import deloitte.score.system.common.Result;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目提交人信息控制器
 */
@Tag(name = "项目提交管理", description = "项目提交相关接口")
@RestController
@RequestMapping("/api/project-submissions")
@CrossOrigin(origins = "*")
public class ProjectSubmissionController {

    @Autowired
    private ProjectSubmissionService projectSubmissionService;

    /**
     * 创建项目提交记录
     */
    @Operation(summary = "创建项目提交记录", description = "创建新的项目提交记录")
    @PostMapping
    public Result<ProjectSubmissionVO> createSubmission(
            @Parameter(description = "项目提交信息", required = true) @RequestBody ProjectSubmissionDTO submissionDTO) {
        try {
            // 检查项目是否已存在
            if (projectSubmissionService.isProjectExists(submissionDTO.getProjectName(), submissionDTO.getSubmitterEmail())) {
                return Result.error("该项目已存在相同提交人的记录");
            }

            ProjectSubmissionVO submissionVO = projectSubmissionService.createSubmission(submissionDTO);
            return Result.success("项目提交记录创建成功", submissionVO);
        } catch (Exception e) {
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 批量创建或更新项目提交记录
     */
    @Operation(summary = "批量创建或更新项目提交记录",
               description = "批量创建或更新项目提交记录，如果项目+提交人已存在则更新，否则创建")
    @PostMapping("/batch")
    public Result<BatchProjectSubmissionVO> batchCreateOrUpdateSubmissions(
            @Parameter(description = "批量项目提交信息", required = true) @RequestBody List<ProjectSubmissionDTO> submissions) {
        try {
            BatchProjectSubmissionDTO batchDTO =  new BatchProjectSubmissionDTO();
             batchDTO.setSubmissions(submissions);
            BatchProjectSubmissionVO result = projectSubmissionService.batchCreateOrUpdateSubmissions(batchDTO);

            String message = String.format("批量处理完成：总数%d，成功%d，失败%d",
                                          result.getTotalCount(), result.getSuccessCount(), result.getFailedCount());

            return Result.success(message, result);
        } catch (Exception e) {
            return Result.error("批量处理失败: " + e.getMessage());
        }
    }

    /**
     * 更新项目提交记录
     */
    @Operation(summary = "更新项目提交记录", description = "更新指定的项目提交记录")
    @PutMapping("/{id}")
    public Result<ProjectSubmissionVO> updateSubmission(
            @Parameter(description = "提交记录ID", required = true) @PathVariable Integer id,
            @Parameter(description = "项目提交信息", required = true) @RequestBody ProjectSubmissionDTO submissionDTO) {
        try {
            ProjectSubmissionVO submissionVO = projectSubmissionService.updateSubmission(id, submissionDTO);
            return Result.success("项目提交记录更新成功", submissionVO);
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询项目提交记录
     */
    @Operation(summary = "根据ID查询项目提交记录", description = "根据ID查询项目提交记录详情")
    @GetMapping("/{id}")
    public Result<ProjectSubmissionVO> getSubmissionById(
            @Parameter(description = "提交记录ID", required = true) @PathVariable Integer id) {
        try {
            ProjectSubmissionVO submissionVO = projectSubmissionService.getSubmissionById(id);
            if (submissionVO != null) {
                return Result.success(submissionVO);
            } else {
                return Result.error("未找到指定的项目提交记录");
            }
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 删除项目提交记录
     */
    @Operation(summary = "删除项目提交记录", description = "根据ID删除项目提交记录")
    @DeleteMapping("/{id}")
    public Result<String> deleteSubmission(
            @Parameter(description = "提交记录ID", required = true) @PathVariable Integer id) {
        try {
            boolean deleted = projectSubmissionService.deleteSubmission(id);
            if (deleted) {
                return Result.success("项目提交记录删除成功");
            } else {
                return Result.error("删除失败，记录不存在");
            }
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询项目提交记录
     */
    @Operation(summary = "分页查询项目提交记录", description = "分页查询项目提交记录列表，支持多条件查询和排序")
    @PostMapping("/page")
    public Result<PageVO<ProjectSubmissionVO>> getSubmissions(
            @Parameter(description = "分页查询参数", required = true) @RequestBody ProjectSubmissionPageDTO pageDTO) {
        try {
            PageVO<ProjectSubmissionVO> pageVO = projectSubmissionService.getSubmissionsByPage(pageDTO);
            return Result.success(pageVO);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据项目名称查询提交记录
     */
    @Operation(summary = "根据项目名称查询提交记录", description = "根据项目名称查询相关提交记录")
    @GetMapping("/project/{projectName}")
    public Result<List<ProjectSubmissionVO>> getSubmissionsByProjectName(
            @Parameter(description = "项目名称", required = true) @PathVariable String projectName) {
        try {
            List<ProjectSubmissionVO> submissions = projectSubmissionService.getSubmissionsByProjectName(projectName);
            return Result.success(submissions);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据提交人姓名查询提交记录
     */
    @Operation(summary = "根据提交人姓名查询提交记录", description = "根据提交人姓名查询相关提交记录")
    @GetMapping("/submitter/{submitterName}")
    public Result<List<ProjectSubmissionVO>> getSubmissionsBySubmitterName(
            @Parameter(description = "提交人姓名", required = true) @PathVariable String submitterName) {
        try {
            List<ProjectSubmissionVO> submissions = projectSubmissionService.getSubmissionsBySubmitterName(submitterName);
            return Result.success(submissions);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 搜索项目提交记录
     */
    @GetMapping("/search")
    public ResponseEntity<Map<String, Object>> searchSubmissions(@RequestParam String keyword) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ProjectSubmission> submissions = projectSubmissionService.searchSubmissions(keyword);
            response.put("success", true);
            response.put("data", submissions);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "搜索失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 查询最近的项目提交记录
     */
    @GetMapping("/recent")
    public ResponseEntity<Map<String, Object>> getRecentSubmissions(@RequestParam(defaultValue = "10") int limit) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ProjectSubmission> submissions = projectSubmissionService.getRecentSubmissions(limit);
            response.put("success", true);
            response.put("data", submissions);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        Map<String, Object> response = new HashMap<>();
        try {
            long totalCount = projectSubmissionService.getSubmissionCount();
            List<ProjectSubmission> recentSubmissions = projectSubmissionService.getRecentSubmissions(5);

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalSubmissions", totalCount);
            statistics.put("recentSubmissions", recentSubmissions);

            response.put("success", true);
            response.put("data", statistics);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 更新项目提交状态
     */
    @Operation(summary = "更新项目提交状态", description = "更新项目提交的完成状态")
    @PutMapping("/{id}/status")
    public Result<String> updateSubmissionStatus(
            @Parameter(description = "提交记录ID", required = true) @PathVariable Integer id,
            @Parameter(description = "状态", required = true, example = "1") @RequestParam String status) {
        try {
            if (!"0".equals(status) && !"1".equals(status)) {
                return Result.error("状态参数错误，只能为0或1");
            }

            boolean updated = projectSubmissionService.updateSubmissionStatus(id, status);
            if (updated) {
                String message = "1".equals(status) ? "状态更新为已完成" : "状态更新为未完成";
                return Result.success(message);
            } else {
                return Result.error("更新失败，记录不存在");
            }
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }
}
