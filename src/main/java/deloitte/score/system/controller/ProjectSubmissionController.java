package deloitte.score.system.controller;

import deloitte.score.system.model.ProjectSubmission;
import deloitte.score.system.service.ProjectSubmissionService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;



import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目提交人信息控制器
 */

@RestController
@RequestMapping("/api/project-submissions")
@CrossOrigin(origins = "*")
public class ProjectSubmissionController {

    @Autowired
    private ProjectSubmissionService projectSubmissionService;

    /**
     * 创建项目提交记录
     */

    @PostMapping
    public ResponseEntity<Map<String, Object>> createSubmission(
            @RequestBody ProjectSubmission submission) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 检查项目是否已存在
            if (projectSubmissionService.isProjectExists(submission.getProjectName(), submission.getSubmitterEmail())) {
                response.put("success", false);
                response.put("message", "该项目已存在相同提交人的记录");
                return ResponseEntity.badRequest().body(response);
            }

            ProjectSubmission created = projectSubmissionService.createSubmission(submission);
            response.put("success", true);
            response.put("message", "项目提交记录创建成功");
            response.put("data", created);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "创建失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 更新项目提交记录
     */

    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateSubmission(
            @PathVariable Integer id,
            @RequestBody ProjectSubmission submission) {
        Map<String, Object> response = new HashMap<>();
        try {
            submission.setId(id);
            ProjectSubmission updated = projectSubmissionService.updateSubmission(submission);
            response.put("success", true);
            response.put("message", "项目提交记录更新成功");
            response.put("data", updated);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据ID查询项目提交记录
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getSubmissionById(@PathVariable Integer id) {
        Map<String, Object> response = new HashMap<>();
        try {
            ProjectSubmission submission = projectSubmissionService.getSubmissionById(id);
            if (submission != null) {
                response.put("success", true);
                response.put("data", submission);
            } else {
                response.put("success", false);
                response.put("message", "未找到指定的项目提交记录");
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 删除项目提交记录
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteSubmission(@PathVariable Integer id) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean deleted = projectSubmissionService.deleteSubmission(id);
            if (deleted) {
                response.put("success", true);
                response.put("message", "项目提交记录删除成功");
            } else {
                response.put("success", false);
                response.put("message", "删除失败，记录不存在");
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 分页查询项目提交记录
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getSubmissions(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        Map<String, Object> response = new HashMap<>();
        try {
            Page<ProjectSubmission> page = projectSubmissionService.getSubmissionsByPage(pageNum, pageSize);
            response.put("success", true);
            response.put("data", page.getRecords());
            response.put("total", page.getTotal());
            response.put("pageNum", page.getCurrent());
            response.put("pageSize", page.getSize());
            response.put("pages", page.getPages());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据项目名称查询提交记录
     */
    @GetMapping("/project/{projectName}")
    public ResponseEntity<Map<String, Object>> getSubmissionsByProjectName(@PathVariable String projectName) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ProjectSubmission> submissions = projectSubmissionService.getSubmissionsByProjectName(projectName);
            response.put("success", true);
            response.put("data", submissions);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据提交人姓名查询提交记录
     */
    @GetMapping("/submitter/{submitterName}")
    public ResponseEntity<Map<String, Object>> getSubmissionsBySubmitterName(@PathVariable String submitterName) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ProjectSubmission> submissions = projectSubmissionService.getSubmissionsBySubmitterName(submitterName);
            response.put("success", true);
            response.put("data", submissions);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 搜索项目提交记录
     */
    @GetMapping("/search")
    public ResponseEntity<Map<String, Object>> searchSubmissions(@RequestParam String keyword) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ProjectSubmission> submissions = projectSubmissionService.searchSubmissions(keyword);
            response.put("success", true);
            response.put("data", submissions);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "搜索失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 查询最近的项目提交记录
     */
    @GetMapping("/recent")
    public ResponseEntity<Map<String, Object>> getRecentSubmissions(@RequestParam(defaultValue = "10") int limit) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ProjectSubmission> submissions = projectSubmissionService.getRecentSubmissions(limit);
            response.put("success", true);
            response.put("data", submissions);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        Map<String, Object> response = new HashMap<>();
        try {
            long totalCount = projectSubmissionService.getSubmissionCount();
            List<ProjectSubmission> recentSubmissions = projectSubmissionService.getRecentSubmissions(5);

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalSubmissions", totalCount);
            statistics.put("recentSubmissions", recentSubmissions);

            response.put("success", true);
            response.put("data", statistics);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
