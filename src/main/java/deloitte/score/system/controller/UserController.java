package deloitte.score.system.controller;

import deloitte.score.system.common.Result;
import deloitte.score.system.dto.UserDTO;
import deloitte.score.system.mapper.UserMapper;
import deloitte.score.system.model.User;
import deloitte.score.system.util.BeanCopyUtils;
import deloitte.score.system.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "用户管理", description = "用户相关接口")
@RestController
@RequestMapping("/api/users")
@CrossOrigin(origins = "*")
public class UserController {

    @Autowired
    private UserMapper userMapper;

    @ApiOperation(value = "获取所有用户", notes = "查询系统中所有用户信息")
    @GetMapping
    public Result<List<UserVO>> getAllUsers() {
        try {
            List<User> users = userMapper.selectList(null);
            List<UserVO> userVOList = BeanCopyUtils.copyBeanList(users, UserVO.class);
            return Result.success("查询成功", userVOList);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建用户", notes = "添加新的用户到系统中")
    @PostMapping
    public Result<UserVO> createUser(
            @ApiParam(value = "用户信息", required = true) @Valid @RequestBody UserDTO userDTO) {
        try {
            User user = BeanCopyUtils.copyBean(userDTO, User.class);
            userMapper.insert(user);
            UserVO userVO = BeanCopyUtils.copyBean(user, UserVO.class);
            return Result.success("用户创建成功", userVO);
        } catch (Exception e) {
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "根据ID获取用户", notes = "根据用户ID查询用户信息")
    @GetMapping("/{id}")
    public Result<UserVO> getUserById(
            @ApiParam(value = "用户ID", required = true) @PathVariable Long id) {
        try {
            User user = userMapper.selectById(id);
            if (user != null) {
                UserVO userVO = BeanCopyUtils.copyBean(user, UserVO.class);
                return Result.success("查询成功", userVO);
            } else {
                return Result.error("用户不存在");
            }
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新用户", notes = "更新用户信息")
    @PutMapping("/{id}")
    public Result<UserVO> updateUser(
            @ApiParam(value = "用户ID", required = true) @PathVariable Long id,
            @ApiParam(value = "用户信息", required = true) @Valid @RequestBody UserDTO userDTO) {
        try {
            User user = BeanCopyUtils.copyBean(userDTO, User.class);
            user.setId(id);
            int result = userMapper.updateById(user);
            if (result > 0) {
                UserVO userVO = BeanCopyUtils.copyBean(user, UserVO.class);
                return Result.success("更新成功", userVO);
            } else {
                return Result.error("用户不存在");
            }
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除用户", notes = "根据用户ID删除用户")
    @DeleteMapping("/{id}")
    public Result<String> deleteUser(
            @ApiParam(value = "用户ID", required = true) @PathVariable Long id) {
        try {
            int result = userMapper.deleteById(id);
            if (result > 0) {
                return Result.success("删除成功", "删除成功");
            } else {
                return Result.error("用户不存在");
            }
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }
}