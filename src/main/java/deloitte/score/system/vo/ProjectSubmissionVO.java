package deloitte.score.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 项目提交视图对象
 */
@Data
@Schema(description = "项目提交信息响应数据")
public class ProjectSubmissionVO {

    private Integer id;

    private String projectName;

    private String submitterName;

    private String submitterEmail;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
