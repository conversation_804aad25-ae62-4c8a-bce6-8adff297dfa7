package deloitte.score.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 项目提交视图对象
 */
@Data
@Schema(description = "项目提交信息响应数据")
public class ProjectSubmissionVO {

    @Schema(description = "项目提交ID", example = "1")
    private Integer id;

    @Schema(description = "项目名称", example = "电商平台后端系统")
    private String projectName;

    @Schema(description = "提交人姓名", example = "张三")
    private String submitterName;

    @Schema(description = "提交人邮箱", example = "<EMAIL>")
    private String submitterEmail;

    @Schema(description = "创建时间", example = "2025-05-30T17:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2025-05-30T17:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
