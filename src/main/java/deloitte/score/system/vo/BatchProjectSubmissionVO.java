package deloitte.score.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 批量项目提交响应视图对象
 */
@Data
@Schema(description = "批量项目提交响应数据")
public class BatchProjectSubmissionVO {

    @Schema(description = "成功创建的记录列表")
    private List<ProjectSubmissionVO> created;

    @Schema(description = "成功更新的记录列表")
    private List<ProjectSubmissionVO> updated;

    @Schema(description = "失败的记录列表")
    private List<FailedSubmissionVO> failed;

    @Schema(description = "总处理数量", example = "10")
    private Integer totalCount;

    @Schema(description = "成功处理数量", example = "8")
    private Integer successCount;

    @Schema(description = "失败处理数量", example = "2")
    private Integer failedCount;

    /**
     * 失败记录详情
     */
    @Data
    @Schema(description = "失败记录详情")
    public static class FailedSubmissionVO {

        @Schema(description = "项目名称", example = "电商平台后端系统")
        private String projectName;

        @Schema(description = "提交人姓名", example = "张三")
        private String submitterName;

        @Schema(description = "提交人邮箱", example = "<EMAIL>")
        private String submitterEmail;

        @Schema(description = "失败原因", example = "数据库连接失败")
        private String errorMessage;

        public FailedSubmissionVO() {}

        public FailedSubmissionVO(String projectName, String submitterName, String submitterEmail, String errorMessage) {
            this.projectName = projectName;
            this.submitterName = submitterName;
            this.submitterEmail = submitterEmail;
            this.errorMessage = errorMessage;
        }
    }
}
