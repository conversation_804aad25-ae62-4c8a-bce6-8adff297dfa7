package deloitte.score.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 项目质量评审视图对象
 */
@Data
@Schema(description = "项目质量评审信息响应数据")
public class ProjectQualityReviewVO {

    @Schema(description = "评审ID", example = "1")
    private Integer id;

    @Schema(description = "项目名称", example = "电商平台后端系统")
    private String projectName;

    @Schema(description = "提交人姓名", example = "张三")
    private String submitterName;

    @Schema(description = "提交人邮箱", example = "<EMAIL>")
    private String submitterEmail;

    @Schema(description = "代码安全性得分", example = "85.50")
    private BigDecimal securityScore;

    @Schema(description = "代码质量得分", example = "88.00")
    private BigDecimal qualityScore;

    @Schema(description = "提交规范得分", example = "82.00")
    private BigDecimal standardScore;

    @Schema(description = "最终得分", example = "85.40")
    private BigDecimal finalScore;

    @Schema(description = "得分等级", example = "良好")
    private String grade;

    @Schema(description = "主要问题", example = "SQL注入风险，部分接口缺少参数验证")
    private String mainIssues;

    @Schema(description = "优化建议", example = "建议加强输入验证，使用参数化查询")
    private String optimizationSuggestions;

    @Schema(description = "评审日期", example = "2025-05-30")
    private LocalDate reviewDate;

    @Schema(description = "创建时间", example = "2025-05-30T17:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间", example = "2025-05-30T17:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
