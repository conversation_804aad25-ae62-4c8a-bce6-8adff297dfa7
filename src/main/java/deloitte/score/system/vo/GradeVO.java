package deloitte.score.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 等级响应视图对象
 */
@Data
@Schema(description = "等级响应数据")
public class GradeVO {
    
    @Schema(description = "分数", example = "85.5")
    private BigDecimal score;
    
    @Schema(description = "等级", example = "良好")
    private String grade;
    
    public GradeVO() {}
    
    public GradeVO(BigDecimal score, String grade) {
        this.score = score;
        this.grade = grade;
    }
}
