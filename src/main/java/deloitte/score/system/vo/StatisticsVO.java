package deloitte.score.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 统计信息视图对象
 */
@Data
@Schema(description = "统计信息响应数据")
public class StatisticsVO {
    
    @Schema(description = "总记录数", example = "100")
    private Long totalCount;
    
    @Schema(description = "平均安全性评分", example = "85.5")
    private BigDecimal avgSecurityScore;
    
    @Schema(description = "平均质量评分", example = "88.0")
    private BigDecimal avgQualityScore;
    
    @Schema(description = "平均标准符合性评分", example = "82.0")
    private BigDecimal avgStandardScore;
    
    @Schema(description = "平均最终评分", example = "85.2")
    private BigDecimal avgFinalScore;
    
    @Schema(description = "各等级统计")
    private Map<String, Long> gradeStatistics;
    
    @Schema(description = "最高分项目列表")
    private List<ProjectQualityReviewVO> topProjects;
    
    @Schema(description = "最近评审列表")
    private List<ProjectQualityReviewVO> recentReviews;
}
