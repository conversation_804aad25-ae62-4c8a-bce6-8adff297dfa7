package deloitte.score.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户视图对象
 */
@Data
@Schema(description = "用户信息响应数据")
public class UserVO {

    @Schema(description = "用户ID", example = "1")
    private Long id;

    @Schema(description = "用户名称", example = "张三")
    private String name;

    @Schema(description = "用户年龄", example = "25")
    private Integer age;

    @Schema(description = "用户邮箱", example = "<EMAIL>")
    private String email;
}
