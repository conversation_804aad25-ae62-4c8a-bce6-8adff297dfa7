package deloitte.score.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 完成率统计视图对象
 */
@Data
@Schema(description = "完成率统计响应数据")
public class CompletionRateVO {

    @Schema(description = "已完成人数（status=1）", example = "85")
    private Long completedCount;

    @Schema(description = "总人数（所有提交记录）", example = "100")
    private Long totalCount;

    @Schema(description = "完成率百分比", example = "85.00")
    private BigDecimal completionRate;

    @Schema(description = "未完成人数（status=0或null）", example = "15")
    private Long incompleteCount;

    @Schema(description = "未完成率百分比", example = "15.00")
    private BigDecimal incompleteRate;

    @Schema(description = "统计说明")
    private String description;

    public CompletionRateVO() {
        this.description = "完成率 = status=1的记录数 / 所有项目提交记录数";
    }
}
