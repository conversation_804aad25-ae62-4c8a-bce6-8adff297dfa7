package deloitte.score.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 完成率统计视图对象
 */
@Data
@Schema(description = "完成率统计响应数据")
public class CompletionRateVO {
    
    @Schema(description = "项目提交完成人数", example = "85")
    private Long submissionCompletedCount;
    
    @Schema(description = "项目提交总人数", example = "100")
    private Long submissionTotalCount;
    
    @Schema(description = "项目提交完成率", example = "85.00")
    private BigDecimal submissionCompletionRate;
    
    @Schema(description = "质量评审完成人数", example = "72")
    private Long reviewCompletedCount;
    
    @Schema(description = "质量评审总人数", example = "85")
    private Long reviewTotalCount;
    
    @Schema(description = "质量评审完成率", example = "84.71")
    private BigDecimal reviewCompletionRate;
    
    @Schema(description = "整体完成人数", example = "72")
    private Long overallCompletedCount;
    
    @Schema(description = "整体总人数", example = "100")
    private Long overallTotalCount;
    
    @Schema(description = "整体完成率", example = "72.00")
    private BigDecimal overallCompletionRate;
    
    @Schema(description = "统计说明")
    private String description;
    
    public CompletionRateVO() {
        this.description = "整体完成率 = 既有项目提交又有质量评审的人数 / 总人数";
    }
}
