package deloitte.score.system.dto;



import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * 项目提交数据传输对象
 */
@Data

public class ProjectSubmissionDTO {
    

    @NotBlank(message = "项目名称不能为空")
    private String projectName;
    

    @NotBlank(message = "提交人姓名不能为�?)
    private String submitterName;
    

    @NotBlank(message = "提交人邮箱不能为�?)
    @Email(message = "邮箱格式不正�?)
    private String submitterEmail;
}
