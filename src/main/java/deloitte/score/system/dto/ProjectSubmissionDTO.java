package deloitte.score.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Email;

/**
 * 项目提交数据传输对象
 */
@Data
@ApiModel(value = "项目提交请求参数", description = "项目提交信息请求参数")
public class ProjectSubmissionDTO {
    
    @ApiModelProperty(value = "项目名称", required = true, example = "电商平台后端系统")
    @NotBlank(message = "项目名称不能为空")
    private String projectName;
    
    @ApiModelProperty(value = "提交人姓名", required = true, example = "张三")
    @NotBlank(message = "提交人姓名不能为空")
    private String submitterName;
    
    @ApiModelProperty(value = "提交人邮箱", required = true, example = "<EMAIL>")
    @NotBlank(message = "提交人邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String submitterEmail;
}
