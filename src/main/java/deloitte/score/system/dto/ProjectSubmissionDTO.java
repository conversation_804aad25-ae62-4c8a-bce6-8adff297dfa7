package deloitte.score.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Email;

/**
 * 项目提交数据传输对象
 */
@Data
@Schema(description = "项目提交信息请求参数")
public class ProjectSubmissionDTO {

    @Schema(description = "项目名称", example = "电商平台后端系统", required = true)
    @NotBlank(message = "项目名称不能为空")
    private String projectName;

    @Schema(description = "提交人姓名", example = "张三", required = true)
    @NotBlank(message = "提交人姓名不能为空")
    private String submitterName;

    @Schema(description = "提交人邮箱", example = "<EMAIL>", required = true)
    @NotBlank(message = "提交人邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String submitterEmail;
}
