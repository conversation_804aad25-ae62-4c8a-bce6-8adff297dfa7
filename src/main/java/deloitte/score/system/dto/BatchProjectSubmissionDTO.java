package deloitte.score.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量项目提交数据传输对象
 */
@Data
@Schema(description = "批量项目提交请求参数")
public class BatchProjectSubmissionDTO {
    
    @Schema(description = "项目提交信息列表", required = true)
    @NotEmpty(message = "提交列表不能为空")
    @Valid
    private List<ProjectSubmissionDTO> submissions;
}
