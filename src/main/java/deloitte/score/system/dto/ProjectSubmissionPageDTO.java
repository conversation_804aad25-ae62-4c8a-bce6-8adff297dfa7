package deloitte.score.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 项目提交分页查询数据传输对象
 */
@Data
@Schema(description = "项目提交分页查询参数")
public class ProjectSubmissionPageDTO {
    
    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    @Schema(description = "每页大小", example = "10")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;
    
    @Schema(description = "项目名称（模糊查询）", example = "电商平台")
    private String projectName;
    
    @Schema(description = "提交人姓名（模糊查询）", example = "张三")
    private String submitterName;
    
    @Schema(description = "提交人邮箱（精确查询）", example = "<EMAIL>")
    private String submitterEmail;
    
    @Schema(description = "排序字段", example = "createTime", allowableValues = {"createTime", "updateTime", "projectName", "submitterName"})
    private String sortField = "createTime";
    
    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortOrder = "desc";
}
