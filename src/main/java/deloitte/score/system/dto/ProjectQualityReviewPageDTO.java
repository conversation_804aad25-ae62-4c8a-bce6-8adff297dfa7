package deloitte.score.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 项目质量评审分页查询数据传输对象
 */
@Data
@Schema(description = "项目质量评审分页查询参数")
public class ProjectQualityReviewPageDTO {
    
    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    @Schema(description = "每页大小", example = "10")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;
    
    @Schema(description = "项目名称（模糊查询）", example = "电商平台")
    private String projectName;
    
    @Schema(description = "提交人姓名（模糊查询）", example = "张三")
    private String submitterName;
    
    @Schema(description = "提交人邮箱（精确查询）", example = "<EMAIL>")
    private String submitterEmail;
    
    @Schema(description = "最低分数", example = "60.0")
    private BigDecimal minScore;
    
    @Schema(description = "最高分数", example = "100.0")
    private BigDecimal maxScore;
    
    @Schema(description = "等级筛选", example = "良好", allowableValues = {"优秀", "良好", "中等", "及格", "不及格"})
    private String grade;
    
    @Schema(description = "开始日期", example = "2025-01-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;
    
    @Schema(description = "结束日期", example = "2025-12-31")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;
    
    @Schema(description = "排序字段", example = "createdAt", 
            allowableValues = {"createdAt", "updatedAt", "reviewDate", "finalScore", "projectName", "submitterName"})
    private String sortField = "createdAt";
    
    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortOrder = "desc";
}
