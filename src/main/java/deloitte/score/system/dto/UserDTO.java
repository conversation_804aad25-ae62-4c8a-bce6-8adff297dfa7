package deloitte.score.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 用户数据传输对象
 */
@Data
@ApiModel(value = "用户请求参数", description = "用户信息请求参数")
public class UserDTO {

    @ApiModelProperty(value = "用户名称", required = true, example = "张三")
    @NotBlank(message = "用户名称不能为空")
    private String name;

    @ApiModelProperty(value = "用户年龄", example = "25")
    @Min(value = 1, message = "年龄必须大于0")
    @Max(value = 150, message = "年龄不能超过150")
    private Integer age;

    @ApiModelProperty(value = "用户邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    private String email;
}
