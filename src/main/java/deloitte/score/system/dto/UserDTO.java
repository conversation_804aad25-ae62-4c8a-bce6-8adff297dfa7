package deloitte.score.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 用户数据传输对象
 */
@Data
@Schema(description = "用户信息请求参数")
public class UserDTO {

    @Schema(description = "用户名称", example = "张三", required = true)
    @NotBlank(message = "用户名称不能为空")
    private String name;

    @Schema(description = "用户年龄", example = "25")
    @Min(value = 1, message = "年龄必须大于0")
    @Max(value = 150, message = "年龄不能超过150")
    private Integer age;

    @Schema(description = "用户邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    private String email;
}
