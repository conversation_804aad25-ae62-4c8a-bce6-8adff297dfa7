package deloitte.score.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Email;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 项目质量评审数据传输对象
 */
@Data
@ApiModel(value = "项目质量评审请求参数", description = "项目质量评审信息请求参数")
public class ProjectQualityReviewDTO {
    
    @ApiModelProperty(value = "项目名称", required = true, example = "电商平台后端系统")
    @NotBlank(message = "项目名称不能为空")
    private String projectName;
    
    @ApiModelProperty(value = "提交人姓名", required = true, example = "张三")
    @NotBlank(message = "提交人姓名不能为空")
    private String submitterName;
    
    @ApiModelProperty(value = "提交人邮箱", required = true, example = "<EMAIL>")
    @NotBlank(message = "提交人邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String submitterEmail;
    
    @ApiModelProperty(value = "安全性评分", example = "85.5")
    @DecimalMin(value = "0.0", message = "安全性评分不能小于0")
    @DecimalMax(value = "100.0", message = "安全性评分不能大于100")
    private BigDecimal securityScore;
    
    @ApiModelProperty(value = "质量评分", example = "90.0")
    @DecimalMin(value = "0.0", message = "质量评分不能小于0")
    @DecimalMax(value = "100.0", message = "质量评分不能大于100")
    private BigDecimal qualityScore;
    
    @ApiModelProperty(value = "标准符合性评分", example = "88.0")
    @DecimalMin(value = "0.0", message = "标准符合性评分不能小于0")
    @DecimalMax(value = "100.0", message = "标准符合性评分不能大于100")
    private BigDecimal standardsScore;
    
    @ApiModelProperty(value = "评审日期", example = "2025-05-30")
    private LocalDate reviewDate;
    
    @ApiModelProperty(value = "评审备注", example = "整体质量良好，建议优化安全性")
    private String reviewComments;
}
