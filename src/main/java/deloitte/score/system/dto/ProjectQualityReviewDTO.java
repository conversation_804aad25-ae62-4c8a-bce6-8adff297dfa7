package deloitte.score.system.dto;



import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 项目质量评审数据传输对象
 */
@Data

public class ProjectQualityReviewDTO {
    

    @NotBlank(message = "项目名称不能为空")
    private String projectName;
    

    @NotBlank(message = "提交人姓名不能为�?)
    private String submitterName;
    

    @NotBlank(message = "提交人邮箱不能为�?)
    @Email(message = "邮箱格式不正�?)
    private String submitterEmail;
    
", required = true, example = "85.5")
    @NotNull(message = "安全性得分不能为�?)
    @DecimalMin(value = "0.0", message = "安全性得分不能小�?")
    @DecimalMax(value = "100.0", message = "安全性得分不能大�?00")
    private BigDecimal securityScore;
    
", required = true, example = "88.0")
    @NotNull(message = "质量得分不能为空")
    @DecimalMin(value = "0.0", message = "质量得分不能小于0")
    @DecimalMax(value = "100.0", message = "质量得分不能大于100")
    private BigDecimal qualityScore;
    
", required = true, example = "82.0")
    @NotNull(message = "规范得分不能为空")
    @DecimalMin(value = "0.0", message = "规范得分不能小于0")
    @DecimalMax(value = "100.0", message = "规范得分不能大于100")
    private BigDecimal standardScore;
    

    private String mainIssues;
    

    private String optimizationSuggestions;
    

    private LocalDate reviewDate;
}
