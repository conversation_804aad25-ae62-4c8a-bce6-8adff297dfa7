package deloitte.score.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Email;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 项目质量评审数据传输对象
 */
@Data
@Schema(description = "项目质量评审信息请求参数")
public class ProjectQualityReviewDTO {

    @Schema(description = "项目名称", example = "电商平台后端系统", required = true)
    @NotBlank(message = "项目名称不能为空")
    private String projectName;

    @Schema(description = "提交人姓名", example = "张三", required = true)
    @NotBlank(message = "提交人姓名不能为空")
    private String submitterName;

    @Schema(description = "提交人邮箱", example = "<EMAIL>", required = true)
    @NotBlank(message = "提交人邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String submitterEmail;

    @Schema(description = "安全性评分", example = "85.5")
    @DecimalMin(value = "0.0", message = "安全性评分不能小于0")
    @DecimalMax(value = "100.0", message = "安全性评分不能大于100")
    private BigDecimal securityScore;

    @Schema(description = "质量评分", example = "90.0")
    @DecimalMin(value = "0.0", message = "质量评分不能小于0")
    @DecimalMax(value = "100.0", message = "质量评分不能大于100")
    private BigDecimal qualityScore;

    @Schema(description = "标准符合性评分", example = "88.0")
    @DecimalMin(value = "0.0", message = "标准符合性评分不能小于0")
    @DecimalMax(value = "100.0", message = "标准符合性评分不能大于100")
    private BigDecimal standardsScore;


    @Schema(description = "评审备注", example = "整体质量良好，建议优化安全性")
    private String reviewComments;
}
