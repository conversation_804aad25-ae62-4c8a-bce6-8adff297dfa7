package deloitte.score.system.service;

import deloitte.score.system.model.ProjectQualityReview;
import deloitte.score.system.mapper.ProjectQualityReviewMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目质量评分服务类
 */
@Service
public class ProjectQualityReviewService {

    @Autowired
    private ProjectQualityReviewMapper projectQualityReviewMapper;

    /**
     * 创建项目质量评审记录
     */
    public ProjectQualityReview createReview(ProjectQualityReview review) {
        // 计算最终得分（如果没有提供的话）
        if (review.getFinalScore() == null) {
            review.setFinalScore(calculateFinalScore(review));
        }

        review.setCreatedAt(LocalDateTime.now());
        review.setUpdatedAt(LocalDateTime.now());

        if (review.getReviewDate() == null) {
            review.setReviewDate(LocalDate.now());
        }

        projectQualityReviewMapper.insert(review);
        return review;
    }

    /**
     * 更新项目质量评审记录
     */
    public ProjectQualityReview updateReview(ProjectQualityReview review) {
        // 重新计算最终得分
        review.setFinalScore(calculateFinalScore(review));
        review.setUpdatedAt(LocalDateTime.now());

        projectQualityReviewMapper.updateById(review);
        return review;
    }

    /**
     * 计算最终得分
     * 权重：安全性30%，质量40%，规范30%
     */
    private BigDecimal calculateFinalScore(ProjectQualityReview review) {
        if (review.getSecurityScore() == null ||
            review.getQualityScore() == null ||
            review.getStandardScore() == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal securityWeight = new BigDecimal("0.30");
        BigDecimal qualityWeight = new BigDecimal("0.40");
        BigDecimal standardWeight = new BigDecimal("0.30");

        BigDecimal finalScore = review.getSecurityScore().multiply(securityWeight)
                .add(review.getQualityScore().multiply(qualityWeight))
                .add(review.getStandardScore().multiply(standardWeight));

        return finalScore.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 根据ID查询评审记录
     */
    public ProjectQualityReview getReviewById(Integer id) {
        return projectQualityReviewMapper.selectById(id);
    }

    /**
     * 删除评审记录
     */
    public boolean deleteReview(Integer id) {
        return projectQualityReviewMapper.deleteById(id) > 0;
    }

    /**
     * 查询所有评审记录
     */
    public List<ProjectQualityReview> getAllReviews() {
        return projectQualityReviewMapper.selectList(null);
    }

    /**
     * 分页查询评审记录
     */
    public Page<ProjectQualityReview> getReviewsByPage(int pageNum, int pageSize) {
        Page<ProjectQualityReview> page = new Page<>(pageNum, pageSize);
        QueryWrapper<ProjectQualityReview> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("created_at");
        return projectQualityReviewMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据项目名称查询评审记录
     */
    public List<ProjectQualityReview> getReviewsByProjectName(String projectName) {
        QueryWrapper<ProjectQualityReview> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_name", projectName)
                   .orderByDesc("created_at");
        return projectQualityReviewMapper.selectList(queryWrapper);
    }

    /**
     * 根据提交人姓名查询评审记录
     */
    public List<ProjectQualityReview> getReviewsBySubmitterName(String submitterName) {
        QueryWrapper<ProjectQualityReview> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("submitter_name", submitterName)
                   .orderByDesc("created_at");
        return projectQualityReviewMapper.selectList(queryWrapper);
    }

    /**
     * 根据得分范围查询评审记录
     */
    public List<ProjectQualityReview> getReviewsByScoreRange(BigDecimal minScore, BigDecimal maxScore) {
        QueryWrapper<ProjectQualityReview> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("final_score", minScore, maxScore)
                   .orderByDesc("final_score");
        return projectQualityReviewMapper.selectList(queryWrapper);
    }

    /**
     * 根据评审日期范围查询评审记录
     */
    public List<ProjectQualityReview> getReviewsByDateRange(LocalDate startDate, LocalDate endDate) {
        QueryWrapper<ProjectQualityReview> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("review_date", startDate, endDate)
                   .orderByDesc("review_date");
        return projectQualityReviewMapper.selectList(queryWrapper);
    }

    /**
     * 获取平均得分统计
     */
    public ProjectQualityReview getAverageScores() {
        // 这个方法仍然使用XML中的复杂查询
        return projectQualityReviewMapper.getAverageScores();
    }

    /**
     * 查询最高得分的项目
     */
    public List<ProjectQualityReview> getTopScoreProjects(int limit) {
        QueryWrapper<ProjectQualityReview> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNotNull("final_score")
                   .orderByDesc("final_score")
                   .last("LIMIT " + limit);
        return projectQualityReviewMapper.selectList(queryWrapper);
    }

    /**
     * 查询最近的评审记录
     */
    public List<ProjectQualityReview> getRecentReviews(int limit) {
        QueryWrapper<ProjectQualityReview> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("created_at")
                   .last("LIMIT " + limit);
        return projectQualityReviewMapper.selectList(queryWrapper);
    }

    /**
     * 根据条件搜索评审记录
     */
    public List<ProjectQualityReview> searchReviews(String keyword) {
        QueryWrapper<ProjectQualityReview> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("project_name", keyword)
                   .or()
                   .like("submitter_name", keyword)
                   .or()
                   .like("main_issues", keyword)
                   .or()
                   .like("optimization_suggestions", keyword);
        return projectQualityReviewMapper.selectList(queryWrapper);
    }

    /**
     * 统计评审记录总数
     */
    public long getReviewCount() {
        return projectQualityReviewMapper.selectCount(null);
    }

    /**
     * 获取得分等级
     */
    public String getScoreGrade(BigDecimal score) {
        if (score == null) {
            return "未评分";
        }

        if (score.compareTo(new BigDecimal("90")) >= 0) {
            return "优秀";
        } else if (score.compareTo(new BigDecimal("80")) >= 0) {
            return "良好";
        } else if (score.compareTo(new BigDecimal("70")) >= 0) {
            return "中等";
        } else if (score.compareTo(new BigDecimal("60")) >= 0) {
            return "及格";
        } else {
            return "不及格";
        }
    }
}
