package deloitte.score.system.service;

import deloitte.score.system.model.ProjectSubmission;
import deloitte.score.system.mapper.ProjectSubmissionMapper;
import deloitte.score.system.dto.ProjectSubmissionDTO;
import deloitte.score.system.dto.BatchProjectSubmissionDTO;
import deloitte.score.system.dto.ProjectSubmissionPageDTO;
import deloitte.score.system.vo.ProjectSubmissionVO;
import deloitte.score.system.vo.BatchProjectSubmissionVO;
import deloitte.score.system.vo.PageVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目提交人信息服务类
 */
@Service
public class ProjectSubmissionService {

    @Autowired
    private ProjectSubmissionMapper projectSubmissionMapper;

    /**
     * 创建项目提交记录
     */
    public ProjectSubmissionVO createSubmission(ProjectSubmissionDTO submissionDTO) {
        // DTO 转实体
        ProjectSubmission submission = new ProjectSubmission();
        BeanUtils.copyProperties(submissionDTO, submission);

        submission.setCreateTime(LocalDateTime.now());
        submission.setUpdateTime(LocalDateTime.now());
        projectSubmissionMapper.insert(submission);

        // 实体转 VO
        return convertToVO(submission);
    }

    /**
     * 批量创建或更新项目提交记录
     */
    @Transactional(rollbackFor = Exception.class)
    public BatchProjectSubmissionVO batchCreateOrUpdateSubmissions(BatchProjectSubmissionDTO batchDTO) {
        BatchProjectSubmissionVO result = new BatchProjectSubmissionVO();
        result.setCreated(new java.util.ArrayList<>());
        result.setUpdated(new java.util.ArrayList<>());
        result.setFailed(new java.util.ArrayList<>());

        int totalCount = batchDTO.getSubmissions().size();
        int successCount = 0;
        int failedCount = 0;

        for (ProjectSubmissionDTO submissionDTO : batchDTO.getSubmissions()) {
            try {
                // 检查是否已存在
                ProjectSubmission existingSubmission = findExistingSubmission(
                    submissionDTO.getProjectName(),
                    submissionDTO.getSubmitterEmail()
                );

                if (existingSubmission != null) {
                    // 存在则更新
                    BeanUtils.copyProperties(submissionDTO, existingSubmission, "id", "createTime");
                    existingSubmission.setUpdateTime(LocalDateTime.now());
                    // 如果没有设置status，默认设为0（未完成）
                    if (existingSubmission.getStatus() == null) {
                        existingSubmission.setStatus("0");
                    }
                    projectSubmissionMapper.updateById(existingSubmission);
                    result.getUpdated().add(convertToVO(existingSubmission));
                } else {
                    // 不存在则创建
                    ProjectSubmission newSubmission = new ProjectSubmission();
                    BeanUtils.copyProperties(submissionDTO, newSubmission);
                    newSubmission.setCreateTime(LocalDateTime.now());
                    newSubmission.setUpdateTime(LocalDateTime.now());
                    projectSubmissionMapper.insert(newSubmission);
                    result.getCreated().add(convertToVO(newSubmission));
                }
                successCount++;
            } catch (Exception e) {
                // 记录失败的记录
                BatchProjectSubmissionVO.FailedSubmissionVO failedRecord =
                    new BatchProjectSubmissionVO.FailedSubmissionVO(
                        submissionDTO.getProjectName(),
                        submissionDTO.getSubmitterName(),
                        submissionDTO.getSubmitterEmail(),
                        e.getMessage()
                    );
                result.getFailed().add(failedRecord);
                failedCount++;
            }
        }

        result.setTotalCount(totalCount);
        result.setSuccessCount(successCount);
        result.setFailedCount(failedCount);

        return result;
    }

    /**
     * 更新项目提交记录
     */
    public ProjectSubmissionVO updateSubmission(Integer id, ProjectSubmissionDTO submissionDTO) {
        // 获取原记录
        ProjectSubmission existingSubmission = projectSubmissionMapper.selectById(id);
        if (existingSubmission == null) {
            throw new RuntimeException("提交记录不存在");
        }

        // 更新字段
        BeanUtils.copyProperties(submissionDTO, existingSubmission, "id", "createTime");
        existingSubmission.setUpdateTime(LocalDateTime.now());

        projectSubmissionMapper.updateById(existingSubmission);
        return convertToVO(existingSubmission);
    }

    /**
     * 根据ID查询项目提交记录
     */
    public ProjectSubmissionVO getSubmissionById(Integer id) {
        ProjectSubmission submission = projectSubmissionMapper.selectById(id);
        return submission != null ? convertToVO(submission) : null;
    }

    /**
     * 删除项目提交记录
     */
    public boolean deleteSubmission(Integer id) {
        return projectSubmissionMapper.deleteById(id) > 0;
    }

    /**
     * 查询所有项目提交记录
     */
    public List<ProjectSubmissionVO> getAllSubmissions() {
        List<ProjectSubmission> submissions = projectSubmissionMapper.selectList(null);
        return submissions.stream()
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 分页查询项目提交记录
     */
    public PageVO<ProjectSubmissionVO> getSubmissionsByPage(ProjectSubmissionPageDTO pageDTO) {
        Page<ProjectSubmission> page = new Page<>(pageDTO.getPageNum(), pageDTO.getPageSize());
        QueryWrapper<ProjectSubmission> queryWrapper = new QueryWrapper<>();

        // 添加查询条件
        if (pageDTO.getProjectName() != null && !pageDTO.getProjectName().trim().isEmpty()) {
            queryWrapper.like("project_name", pageDTO.getProjectName());
        }
        if (pageDTO.getSubmitterName() != null && !pageDTO.getSubmitterName().trim().isEmpty()) {
            queryWrapper.like("submitter_name", pageDTO.getSubmitterName());
        }
        if (pageDTO.getSubmitterEmail() != null && !pageDTO.getSubmitterEmail().trim().isEmpty()) {
            queryWrapper.eq("submitter_email", pageDTO.getSubmitterEmail());
        }

        // 添加排序
        if ("asc".equalsIgnoreCase(pageDTO.getSortOrder())) {
            queryWrapper.orderByAsc(convertSortField(pageDTO.getSortField()));
        } else {
            queryWrapper.orderByDesc(convertSortField(pageDTO.getSortField()));
        }

        Page<ProjectSubmission> resultPage = projectSubmissionMapper.selectPage(page, queryWrapper);

        // 转换为 VO
        List<ProjectSubmissionVO> voList = resultPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());

        return new PageVO<>(voList, resultPage.getTotal(), resultPage.getPages(),
                           resultPage.getCurrent(), resultPage.getSize());
    }

    /**
     * 根据项目名称查询提交记录
     */
    public List<ProjectSubmissionVO> getSubmissionsByProjectName(String projectName) {
        QueryWrapper<ProjectSubmission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_name", projectName)
                   .orderByDesc("create_time");
        List<ProjectSubmission> submissions = projectSubmissionMapper.selectList(queryWrapper);
        return submissions.stream()
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 根据提交人姓名查询提交记录
     */
    public List<ProjectSubmissionVO> getSubmissionsBySubmitterName(String submitterName) {
        QueryWrapper<ProjectSubmission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("submitter_name", submitterName)
                   .orderByDesc("create_time");
        List<ProjectSubmission> submissions = projectSubmissionMapper.selectList(queryWrapper);
        return submissions.stream()
                .map(this::convertToVO)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 根据提交人邮箱查询提交记录
     */
    public List<ProjectSubmission> getSubmissionsBySubmitterEmail(String submitterEmail) {
        QueryWrapper<ProjectSubmission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("submitter_email", submitterEmail)
                   .orderByDesc("create_time");
        return projectSubmissionMapper.selectList(queryWrapper);
    }

    /**
     * 查询最近的项目提交记录
     */
    public List<ProjectSubmission> getRecentSubmissions(int limit) {
        QueryWrapper<ProjectSubmission> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("create_time")
                   .last("LIMIT " + limit);
        return projectSubmissionMapper.selectList(queryWrapper);
    }

    /**
     * 根据条件搜索项目提交记录
     */
    public List<ProjectSubmission> searchSubmissions(String keyword) {
        QueryWrapper<ProjectSubmission> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("project_name", keyword)
                   .or()
                   .like("submitter_name", keyword)
                   .or()
                   .like("submitter_email", keyword);
        return projectSubmissionMapper.selectList(queryWrapper);
    }

    /**
     * 统计项目提交总数
     */
    public long getSubmissionCount() {
        return projectSubmissionMapper.selectCount(null);
    }

    /**
     * 检查项目是否已存在
     */
    public boolean isProjectExists(String projectName, String submitterEmail) {
        QueryWrapper<ProjectSubmission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_name", projectName)
                   .eq("submitter_email", submitterEmail);
        return projectSubmissionMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 查找已存在的提交记录
     */
    public ProjectSubmission findExistingSubmission(String projectName, String submitterEmail) {
        QueryWrapper<ProjectSubmission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_name", projectName)
                   .eq("submitter_email", submitterEmail);
        return projectSubmissionMapper.selectOne(queryWrapper);
    }

    /**
     * 转换排序字段
     */
    private String convertSortField(String sortField) {
        if (sortField == null) {
            return "create_time";
        }
        switch (sortField) {
            case "createTime":
                return "create_time";
            case "updateTime":
                return "update_time";
            case "projectName":
                return "project_name";
            case "submitterName":
                return "submitter_name";
            default:
                return "create_time";
        }
    }

    /**
     * 实体转 VO
     */
    private ProjectSubmissionVO convertToVO(ProjectSubmission submission) {
        ProjectSubmissionVO vo = new ProjectSubmissionVO();
        BeanUtils.copyProperties(submission, vo);
        return vo;
    }
}
