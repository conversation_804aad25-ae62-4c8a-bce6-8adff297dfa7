package deloitte.score.system.service;

import deloitte.score.system.model.ProjectSubmission;
import deloitte.score.system.mapper.ProjectSubmissionMapper;
import deloitte.score.system.dto.ProjectSubmissionDTO;
import deloitte.score.system.vo.ProjectSubmissionVO;
import deloitte.score.system.vo.PageVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目提交人信息服务类
 */
@Service
public class ProjectSubmissionService {

    @Autowired
    private ProjectSubmissionMapper projectSubmissionMapper;

    /**
     * 创建项目提交记录
     */
    public ProjectSubmission createSubmission(ProjectSubmission submission) {
        submission.setCreateTime(LocalDateTime.now());
        submission.setUpdateTime(LocalDateTime.now());
        projectSubmissionMapper.insert(submission);
        return submission;
    }

    /**
     * 更新项目提交记录
     */
    public ProjectSubmission updateSubmission(ProjectSubmission submission) {
        submission.setUpdateTime(LocalDateTime.now());
        projectSubmissionMapper.updateById(submission);
        return submission;
    }

    /**
     * 根据ID查询项目提交记录
     */
    public ProjectSubmission getSubmissionById(Integer id) {
        return projectSubmissionMapper.selectById(id);
    }

    /**
     * 删除项目提交记录
     */
    public boolean deleteSubmission(Integer id) {
        return projectSubmissionMapper.deleteById(id) > 0;
    }

    /**
     * 查询所有项目提交记录
     */
    public List<ProjectSubmission> getAllSubmissions() {
        return projectSubmissionMapper.selectList(null);
    }

    /**
     * 分页查询项目提交记录
     */
    public Page<ProjectSubmission> getSubmissionsByPage(int pageNum, int pageSize) {
        Page<ProjectSubmission> page = new Page<>(pageNum, pageSize);
        return projectSubmissionMapper.selectPage(page, null);
    }

    /**
     * 根据项目名称查询提交记录
     */
    public List<ProjectSubmission> getSubmissionsByProjectName(String projectName) {
        QueryWrapper<ProjectSubmission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_name", projectName)
                   .orderByDesc("create_time");
        return projectSubmissionMapper.selectList(queryWrapper);
    }

    /**
     * 根据提交人姓名查询提交记录
     */
    public List<ProjectSubmission> getSubmissionsBySubmitterName(String submitterName) {
        QueryWrapper<ProjectSubmission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("submitter_name", submitterName)
                   .orderByDesc("create_time");
        return projectSubmissionMapper.selectList(queryWrapper);
    }

    /**
     * 根据提交人邮箱查询提交记录
     */
    public List<ProjectSubmission> getSubmissionsBySubmitterEmail(String submitterEmail) {
        QueryWrapper<ProjectSubmission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("submitter_email", submitterEmail)
                   .orderByDesc("create_time");
        return projectSubmissionMapper.selectList(queryWrapper);
    }

    /**
     * 查询最近的项目提交记录
     */
    public List<ProjectSubmission> getRecentSubmissions(int limit) {
        QueryWrapper<ProjectSubmission> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("create_time")
                   .last("LIMIT " + limit);
        return projectSubmissionMapper.selectList(queryWrapper);
    }

    /**
     * 根据条件搜索项目提交记录
     */
    public List<ProjectSubmission> searchSubmissions(String keyword) {
        QueryWrapper<ProjectSubmission> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("project_name", keyword)
                   .or()
                   .like("submitter_name", keyword)
                   .or()
                   .like("submitter_email", keyword);
        return projectSubmissionMapper.selectList(queryWrapper);
    }

    /**
     * 统计项目提交总数
     */
    public long getSubmissionCount() {
        return projectSubmissionMapper.selectCount(null);
    }

    /**
     * 检查项目是否已存在
     */
    public boolean isProjectExists(String projectName, String submitterEmail) {
        QueryWrapper<ProjectSubmission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_name", projectName)
                   .eq("submitter_email", submitterEmail);
        return projectSubmissionMapper.selectCount(queryWrapper) > 0;
    }
}
