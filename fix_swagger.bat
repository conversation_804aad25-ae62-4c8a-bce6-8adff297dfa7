@echo off
echo Removing Swagger imports and annotations...

REM Remove Swagger imports from all Java files
for /r src %%f in (*.java) do (
    powershell -Command "(Get-Content '%%f') -replace 'import io\.swagger\.annotations\.[^;]+;', '' | Set-Content '%%f'"
    powershell -Command "(Get-Content '%%f') -replace 'import com\.github\.xiaoymin\.knife4j\.[^;]+;', '' | Set-Content '%%f'"
    powershell -Command "(Get-Content '%%f') -replace 'import springfox\.[^;]+;', '' | Set-Content '%%f'"
    powershell -Command "(Get-Content '%%f') -replace '@Api\([^)]*\)', '' | Set-Content '%%f'"
    powershell -Command "(Get-Content '%%f') -replace '@ApiModel\([^)]*\)', '' | Set-Content '%%f'"
    powershell -Command "(Get-Content '%%f') -replace '    @ApiModelProperty\([^)]*\)', '' | Set-Content '%%f'"
    powershell -Command "(Get-Content '%%f') -replace '    @ApiOperation\([^)]*\)', '' | Set-Content '%%f'"
    powershell -Command "(Get-Content '%%f') -replace '@ApiParam\([^)]*\)\s+', '' | Set-Content '%%f'"
    powershell -Command "(Get-Content '%%f') -replace '@EnableSwagger2WebMvc', '' | Set-Content '%%f'"
    powershell -Command "(Get-Content '%%f') -replace '@EnableKnife4j', '' | Set-Content '%%f'"
)

echo Done!
