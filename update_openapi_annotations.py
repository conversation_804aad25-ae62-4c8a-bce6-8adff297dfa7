#!/usr/bin/env python3
import os
import re

def update_openapi_annotations(file_path):
    """更新文件中的注解从 Swagger 2 到 OpenAPI 3"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 更新导入
    content = re.sub(r'import io\.swagger\.annotations\.Api;', 'import io.swagger.v3.oas.annotations.tags.Tag;', content)
    content = re.sub(r'import io\.swagger\.annotations\.ApiModel;', 'import io.swagger.v3.oas.annotations.media.Schema;', content)
    content = re.sub(r'import io\.swagger\.annotations\.ApiModelProperty;', 'import io.swagger.v3.oas.annotations.media.Schema;', content)
    content = re.sub(r'import io\.swagger\.annotations\.ApiOperation;', 'import io.swagger.v3.oas.annotations.Operation;', content)
    content = re.sub(r'import io\.swagger\.annotations\.ApiParam;', 'import io.swagger.v3.oas.annotations.Parameter;', content)
    
    # 更新注解
    content = re.sub(r'@Api\(tags = "([^"]*)"[^)]*\)', r'@Tag(name = "\1", description = "\1相关接口")', content)
    content = re.sub(r'@ApiModel\([^)]*\)', '@Schema', content)
    content = re.sub(r'@ApiModelProperty\(([^)]*)\)', r'@Schema(\1)', content)
    content = re.sub(r'@ApiOperation\(value = "([^"]*)"[^)]*\)', r'@Operation(summary = "\1")', content)
    content = re.sub(r'@ApiParam\(([^)]*)\)', r'@Parameter(\1)', content)
    
    # 修复 Schema 注解中的参数名
    content = re.sub(r'@Schema\(value = "([^"]*)"', r'@Schema(description = "\1"', content)
    content = re.sub(r'@Parameter\(value = "([^"]*)"', r'@Parameter(description = "\1"', content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def process_java_files(directory):
    """处理目录中的所有Java文件"""
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.java'):
                file_path = os.path.join(root, file)
                print(f"Processing: {file_path}")
                update_openapi_annotations(file_path)

if __name__ == "__main__":
    # 处理src目录下的所有Java文件
    process_java_files("src")
    print("OpenAPI annotations update completed!")
